# Modeling Panels for Ultimate Blender Addon
# Unified interface for all modeling tools

import bpy
from bpy.types import Panel

class ULTIMATE_PT_modeling_panel(Panel):
    """Main modeling tools panel"""
    bl_label = "Modeling Tools"
    bl_idname = "ULTIMATE_PT_modeling_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_order = 2

    def draw(self, context):
        layout = self.layout

        # Distribute Tools
        box = layout.box()
        box.label(text="Distribute Objects", icon='OBJECT_DATAMODE')

        col = box.column(align=True)
        col.operator("ultimate_modeling.distribute_x", text="Distribute X", icon='AXIS_SIDE')
        col.operator("ultimate_modeling.distribute_y", text="Distribute Y", icon='AXIS_FRONT')
        col.operator("ultimate_modeling.distribute_z", text="Distribute Z", icon='AXIS_TOP')

        box.label(text="Select 2+ objects to distribute evenly", icon='INFO')

        # Placeholder for future tools
        layout.separator()
        layout.label(text="Additional modeling tools:", icon='TOOL_SETTINGS')
        layout.label(text="• GeoMatch (coming soon)")
        layout.label(text="• Object Display (coming soon)")
        layout.label(text="• Blueprints (coming soon)")

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_PT_modeling_panel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
