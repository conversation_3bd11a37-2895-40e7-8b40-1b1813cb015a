# Reference Panels for Ultimate Blender Addon
# Unified interface for all reference tools

import bpy
from bpy.types import Panel

class ULTIMATE_PT_reference_panel(Panel):
    """Main reference tools panel"""
    bl_label = "Reference Tools"
    bl_idname = "ULTIMATE_PT_reference_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_order = 5

    def draw(self, context):
        layout = self.layout

        # Link Manager Section
        box = layout.box()
        box.label(text="Link Manager", icon='LINKED')

        # Get current linked libraries
        current_libraries = [lib.filepath for lib in bpy.data.libraries]

        if current_libraries:
            for lib_path in current_libraries:
                import os
                row = box.row(align=True)

                # Expand/collapse button
                from ..modules.reference.link_manager import expanded_states, normalize_filepath
                norm_path = normalize_filepath(lib_path)
                expanded = expanded_states.get(norm_path, False)
                row.operator("ultimate_reference.toggle_expand", text="",
                           icon="TRIA_DOWN" if expanded else "TRIA_RIGHT",
                           emboss=False).filepath = lib_path

                # Library name
                row.label(text=os.path.basename(lib_path))

                # Visibility toggle
                from ..modules.reference.link_manager import link_active_states
                is_visible = link_active_states.get(norm_path, True)
                row.operator("ultimate_reference.load_unload", text="",
                           icon="HIDE_OFF" if is_visible else "HIDE_ON").filepath = lib_path

                # Relocate button
                row.operator("ultimate_reference.relocate", text="", icon="GRAPH").original_filepath = lib_path

                # Reload button
                row.operator("ultimate_reference.reload", text="", icon="FILE_REFRESH").filepath = lib_path

                # Remove button
                row.operator("ultimate_reference.remove", text="", icon="X").filepath = lib_path

                # Show full path when expanded
                if expanded:
                    box.row().label(text=lib_path, icon='FILE_FOLDER')
        else:
            box.label(text="No linked files in scene", icon='INFO')

        # Add Link button
        box.separator()
        box.operator("wm.link", text="Add Link", icon="ADD")

        # Placeholder for future tools
        layout.separator()
        layout.label(text="Additional reference tools:", icon='TOOL_SETTINGS')
        layout.label(text="• Blueprints (coming soon)")
        layout.label(text="• Save Selection (coming soon)")

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_PT_reference_panel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
