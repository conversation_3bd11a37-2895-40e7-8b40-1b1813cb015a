# Reference Panels for Ultimate Blender Addon
# Unified interface for all reference tools

import bpy
from bpy.types import Panel

class ULTIMATE_PT_reference_panel(Panel):
    """Main reference tools panel"""
    bl_label = "Reference Tools"
    bl_idname = "ULTIMATE_PT_reference_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_order = 5

    def draw(self, context):
        layout = self.layout
        layout.label(text="Reference tools will be added here")
        layout.label(text="(Reference Editor, Link Manager, etc.)")

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_PT_reference_panel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
