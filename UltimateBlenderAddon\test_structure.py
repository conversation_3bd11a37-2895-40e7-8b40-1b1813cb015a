#!/usr/bin/env python3
"""
Test script to verify the Ultimate Blender Addon structure
This can be run outside of Blender to check imports and structure
"""

import os
import sys

def test_addon_structure():
    """Test that all expected files and modules exist"""
    print("Testing Ultimate Blender Addon structure...")
    
    # Check main files
    required_files = [
        "__init__.py",
        "blender_manifest.toml",
        "modules/__init__.py",
        "ui/__init__.py",
    ]
    
    # Check module files
    module_categories = ["animation", "modeling", "rendering", "lighting", "reference", "viewport", "utils"]
    for category in module_categories:
        required_files.append(f"modules/{category}/__init__.py")
    
    # Check UI files
    ui_files = ["main_panel.py", "animation_panels.py", "modeling_panels.py", 
                "rendering_panels.py", "lighting_panels.py", "reference_panels.py", "viewport_panels.py"]
    for ui_file in ui_files:
        required_files.append(f"ui/{ui_file}")
    
    # Check animation modules
    animation_modules = ["motion_path.py", "copy_shapekeys.py", "render_range.py"]
    for module in animation_modules:
        required_files.append(f"modules/animation/{module}")
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("✗ Missing files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("✓ All required files exist")
    
    # Test import structure (without bpy)
    try:
        # Test that we can at least parse the files
        with open("__init__.py", 'r') as f:
            content = f.read()
            if "bl_info" in content and "Ultimate Blender Addon" in content:
                print("✓ Main __init__.py has correct structure")
            else:
                print("✗ Main __init__.py missing required elements")
                return False
                
        with open("blender_manifest.toml", 'r') as f:
            content = f.read()
            if "Ultimate Blender Addon" in content:
                print("✓ blender_manifest.toml exists and has correct name")
            else:
                print("✗ blender_manifest.toml missing or incorrect")
                return False
                
        print("✓ File structure validation complete")
        return True
        
    except Exception as e:
        print(f"✗ Error testing structure: {e}")
        return False

def main():
    """Main test function"""
    if test_addon_structure():
        print("\n🎉 Ultimate Blender Addon structure is valid!")
        print("The addon should be ready for testing in Blender.")
        return 0
    else:
        print("\n❌ Structure validation failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
