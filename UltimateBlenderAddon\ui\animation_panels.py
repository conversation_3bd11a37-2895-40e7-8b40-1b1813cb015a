# Animation Panels for Ultimate Blender Addon
# Unified interface for all animation tools

import bpy
from bpy.types import Panel

class ULTIMATE_PT_animation_panel(Panel):
    """Main animation tools panel"""
    bl_label = "Animation Tools"
    bl_idname = "ULTIMATE_PT_animation_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_order = 1

    def draw(self, context):
        layout = self.layout
        
        # Motion Path Tools Section
        box = layout.box()
        box.label(text="Motion Paths", icon='ANIM')
        
        settings = context.scene.ultimate_motion_path_settings
        
        # Frame range settings
        row = box.row()
        row.prop(settings, "use_timeline", text="Use Timeline")
        
        if not settings.use_timeline:
            row = box.row()
            row.prop(settings, "start_frame")
            row.prop(settings, "end_frame")
        
        # Path creation tools
        col = box.column(align=True)
        col.operator("ultimate_anim.motion_path_create_object", text="Object Paths", icon='OBJECT_DATA')
        col.operator("ultimate_anim.motion_path_create_vertex", text="Vertex Paths", icon='VERTEXSEL')
        col.operator("ultimate_anim.motion_path_create_bone", text="Bone Paths", icon='BONE_DATA')
        
        # Path settings
        row = box.row()
        row.prop(settings, "icosphere_radius", text="Marker Size")
        row.prop(settings, "marker_step", text="Step")
        
        # Path management
        row = box.row()
        row.operator("ultimate_anim.motion_path_refresh_all", text="Refresh All", icon='FILE_REFRESH')
        row.operator("ultimate_anim.motion_path_delete_all", text="Delete All", icon='TRASH')

class ULTIMATE_PT_shapekeys_panel(Panel):
    """Shapekey tools panel"""
    bl_label = "Shapekey Tools"
    bl_idname = "ULTIMATE_PT_shapekeys_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_parent_id = "ULTIMATE_PT_animation_panel"
    bl_order = 2

    def draw(self, context):
        layout = self.layout
        
        settings = context.scene.ultimate_copy_shapekeys_settings
        
        # Instructions
        layout.label(text="Select sources then target and click:")
        
        # Settings
        col = layout.column(align=True)
        col.prop(settings, "name_only", text="Name Only")
        col.prop(settings, "active_only", text="Active Only")
        
        # Operations
        col = layout.column(align=True)
        col.operator("ultimate_anim.copy_shapekeys", text="Copy Shapekeys", icon='COPYDOWN')
        col.operator("ultimate_anim.copy_shapekey_animation", text="Copy Animation", icon='ANIM')

class ULTIMATE_PT_render_range_panel(Panel):
    """Render range and notes panel"""
    bl_label = "Render Range & Notes"
    bl_idname = "ULTIMATE_PT_render_range_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_parent_id = "ULTIMATE_PT_animation_panel"
    bl_order = 3

    def draw(self, context):
        layout = self.layout
        
        settings = context.scene.ultimate_render_range_settings
        
        # Frame range controls
        box = layout.box()
        box.label(text="Frame Range", icon='PREVIEW_RANGE')
        
        # Current range display
        scene = context.scene
        row = box.row()
        row.label(text=f"Current: {scene.frame_start} - {scene.frame_end}")
        
        # Custom range fields
        row = box.row()
        row.prop(settings, "start", text="Start")
        row.operator("ultimate_anim.copy_start_to_frame_start", text="", icon='FORWARD')
        
        row = box.row()
        row.prop(settings, "end", text="End")
        row.operator("ultimate_anim.copy_end_to_frame_end", text="", icon='FORWARD')
        
        # Range operations
        row = box.row()
        row.operator("ultimate_anim.copy_current_range", text="Copy Current", icon='COPYDOWN')
        row.operator("ultimate_anim.apply_range", text="Apply Range", icon='CHECKMARK')
        
        # Notes section
        box = layout.box()
        box.label(text="Notes", icon='TEXT')
        
        col = box.column()
        col.prop(settings, "notes", text="")
        col.prop(settings, "note_2", text="")
        col.prop(settings, "note_3", text="")
        col.prop(settings, "note_4", text="")
        col.prop(settings, "note_5", text="")
        
        box.operator("ultimate_anim.clear_notes", text="Clear All Notes", icon='TRASH')

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_PT_animation_panel,
    ULTIMATE_PT_shapekeys_panel,
    ULTIMATE_PT_render_range_panel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
