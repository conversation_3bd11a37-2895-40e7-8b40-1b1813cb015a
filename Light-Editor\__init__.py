bl_info = {
    "name": "Light Editor",
    "author": "<PERSON> aka <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>",
    "location": "3Dview > Light Editor",
    "version": (1, 9, 7),
    "blender": (4, 2, 0),
    "description": "A Light Editor and Light Linking addon",
    "category": "Object",
}

# __init__.py
import bpy

# Import your submodules:
from . import LightEditor
from . import Linking
from . import LightGroup

def register():
    LightEditor.register()
    Linking.register()
    LightGroup.register()

def unregister():
    # Unregister in reverse order (best practice)
    LightGroup.unregister()
    Linking.unregister()
    LightEditor.unregister()

if __name__ == "__main__":
    register()
