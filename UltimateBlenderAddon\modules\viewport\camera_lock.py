# Camera Lock - Adapted for Ultimate Blender Addon
# Toggle lock camera and/or cursor functionality
# Original by Blender Bob / ChatGPT

import bpy
from ...utils.common import report_info

class ULTIMATE_VIEWPORT_OT_toggle_lock_camera(bpy.types.Operator):
    """Toggle camera lock in viewport"""
    bl_idname = "ultimate_viewport.toggle_lock_camera"
    bl_label = "Toggle Camera Lock"
    bl_description = "Toggle lock camera in viewport"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        view3d = context.space_data
        view3d.lock_camera = not view3d.lock_camera

        status = "enabled" if view3d.lock_camera else "disabled"
        report_info(self, f"Camera lock {status}")
        return {'FINISHED'}

class ULTIMATE_VIEWPORT_OT_toggle_lock_cursor(bpy.types.Operator):
    """Toggle cursor lock in viewport"""
    bl_idname = "ultimate_viewport.toggle_lock_cursor"
    bl_label = "Toggle Cursor Lock"
    bl_description = "Toggle lock cursor in viewport"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        view3d = context.space_data
        view3d.lock_cursor = not view3d.lock_cursor

        status = "enabled" if view3d.lock_cursor else "disabled"
        report_info(self, f"Cursor lock {status}")
        return {'FINISHED'}

def ultimate_toggle_lock_camera_button(self, context):
    """Add camera lock button to header"""
    if context.space_data.lock_camera:
        icon = 'OUTLINER_OB_CAMERA'
    else:
        icon = 'CAMERA_DATA'
    self.layout.operator(ULTIMATE_VIEWPORT_OT_toggle_lock_camera.bl_idname, text="", icon=icon)

def ultimate_toggle_lock_cursor_button(self, context):
    """Add cursor lock button to header"""
    if context.space_data.lock_cursor:
        icon = 'PIVOT_CURSOR'
    else:
        icon = 'CURSOR'
    self.layout.operator(ULTIMATE_VIEWPORT_OT_toggle_lock_cursor.bl_idname, text="", icon=icon)

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_VIEWPORT_OT_toggle_lock_camera,
    ULTIMATE_VIEWPORT_OT_toggle_lock_cursor,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

    # Add buttons to header
    bpy.types.VIEW3D_HT_header.append(ultimate_toggle_lock_camera_button)
    bpy.types.VIEW3D_HT_header.append(ultimate_toggle_lock_cursor_button)
    print("    Camera Lock registration complete")

def unregister():
    # Remove buttons from header
    bpy.types.VIEW3D_HT_header.remove(ultimate_toggle_lock_cursor_button)
    bpy.types.VIEW3D_HT_header.remove(ultimate_toggle_lock_camera_button)

    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
