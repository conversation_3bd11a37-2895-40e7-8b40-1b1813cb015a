# Distribute - Adapted for Ultimate Blender Addon
# Distribute objects evenly on X, Y, and Z axes
# Original by Blen<PERSON> Bob

import bpy
from ...utils.common import report_info, report_error, get_selected_objects_safe

class ULTIMATE_MODELING_OT_distribute_selected(bpy.types.Operator):
    """Distribute selected objects evenly along specified axis"""
    bl_idname = "ultimate_modeling.distribute_selected"
    bl_label = "Distribute Selected"
    bl_description = "Distribute selected objects evenly along specified axis"
    bl_options = {'REGISTER', 'UNDO'}

    axis: bpy.props.EnumProperty(
        name="Axis",
        description="Axis to distribute along",
        items=[
            ('0', 'X', 'Distribute along X axis'),
            ('1', 'Y', 'Distribute along Y axis'),
            ('2', 'Z', 'Distribute along Z axis'),
        ],
        default='0'
    )

    @classmethod
    def poll(cls, context):
        """Check if we have at least 2 objects selected"""
        return len(get_selected_objects_safe()) >= 2

    def execute(self, context):
        selected_objects = get_selected_objects_safe()

        if len(selected_objects) < 2:
            report_error(self, "Need at least 2 objects selected")
            return {'CANCELLED'}

        # Sort objects by their position on the specified axis
        axis_index = int(self.axis)
        selected_objects.sort(key=lambda obj: obj.location[axis_index])

        # Get the first and last selected objects
        first_object = selected_objects[0]
        last_object = selected_objects[-1]

        # Calculate the total distance between the first and last objects
        total_distance = last_object.location[axis_index] - first_object.location[axis_index]

        # Calculate the spacing between each object
        if len(selected_objects) > 1:
            spacing = total_distance / (len(selected_objects) - 1)
        else:
            spacing = 0

        # Loop through the selected objects and set their position on the specified axis
        for i, obj in enumerate(selected_objects):
            pos = list(obj.location)
            pos[axis_index] = first_object.location[axis_index] + (i * spacing)
            obj.location = tuple(pos)

        axis_names = ['X', 'Y', 'Z']
        report_info(self, f"Distributed {len(selected_objects)} objects along {axis_names[axis_index]} axis")
        return {'FINISHED'}

class ULTIMATE_MODELING_OT_distribute_x(bpy.types.Operator):
    """Distribute selected objects evenly along X axis"""
    bl_idname = "ultimate_modeling.distribute_x"
    bl_label = "Distribute X"
    bl_description = "Distribute selected objects evenly along X axis"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        return bpy.ops.ultimate_modeling.distribute_selected(axis='0')

class ULTIMATE_MODELING_OT_distribute_y(bpy.types.Operator):
    """Distribute selected objects evenly along Y axis"""
    bl_idname = "ultimate_modeling.distribute_y"
    bl_label = "Distribute Y"
    bl_description = "Distribute selected objects evenly along Y axis"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        return bpy.ops.ultimate_modeling.distribute_selected(axis='1')

class ULTIMATE_MODELING_OT_distribute_z(bpy.types.Operator):
    """Distribute selected objects evenly along Z axis"""
    bl_idname = "ultimate_modeling.distribute_z"
    bl_label = "Distribute Z"
    bl_description = "Distribute selected objects evenly along Z axis"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        return bpy.ops.ultimate_modeling.distribute_selected(axis='2')

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_MODELING_OT_distribute_selected,
    ULTIMATE_MODELING_OT_distribute_x,
    ULTIMATE_MODELING_OT_distribute_y,
    ULTIMATE_MODELING_OT_distribute_z,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    print("    Distribute registration complete")

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
