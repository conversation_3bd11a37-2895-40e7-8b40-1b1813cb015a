import bpy
from bpy.types import Panel

class ULTIMATE_PT_main_panel(Panel):
    """Main panel for Ultimate Blender Addon"""
    bl_label = "Ultimate Blender Addon"
    bl_idname = "ULTIMATE_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_order = 0

    def draw(self, context):
        layout = self.layout
        
        # Header with addon info
        box = layout.box()
        col = box.column(align=True)
        col.label(text="Ultimate Blender Addon v1.0.0", icon='BLENDER')
        col.label(text="by Blender Bob", icon='USER')
        
        # Quick info
        layout.separator()
        col = layout.column(align=True)
        col.label(text="Comprehensive Blender Tools:", icon='TOOL_SETTINGS')
        col.label(text="• Animation & Motion")
        col.label(text="• Modeling & Geometry")
        col.label(text="• Rendering & Output")
        col.label(text="• Lighting & Linking")
        col.label(text="• Reference & Import")
        col.label(text="• Viewport & Camera")
        col.label(text="• Utilities & Spreadsheet")
        
        layout.separator()
        
        # Quick access buttons for animation tools (currently implemented)
        box = layout.box()
        box.label(text="Quick Access - Animation Tools:", icon='ANIM')

        row = box.row(align=True)
        row.operator("ultimate_anim.motion_path_create_object", text="Object Paths", icon='OBJECT_DATA')
        row.operator("ultimate_anim.motion_path_create_vertex", text="Vertex Paths", icon='VERTEXSEL')

        row = box.row(align=True)
        row.operator("ultimate_anim.copy_shapekeys", text="Copy Shapekeys", icon='COPYDOWN')
        row.operator("ultimate_anim.copy_current_range", text="Copy Range", icon='PREVIEW_RANGE')

        # Status of tool categories
        box = layout.box()
        box.label(text="Tool Categories Status:", icon='INFO')

        col = box.column(align=True)
        col.label(text="✓ Animation Tools - Ready", icon='CHECKMARK')
        col.label(text="○ Modeling Tools - Placeholder", icon='MESH_DATA')
        col.label(text="○ Rendering Tools - Placeholder", icon='RENDER_STILL')
        col.label(text="○ Lighting Tools - Placeholder", icon='LIGHT')
        col.label(text="○ Reference Tools - Placeholder", icon='FILE_IMAGE')
        col.label(text="○ Viewport Tools - Placeholder", icon='VIEW3D')

        # Global settings
        layout.separator()
        box = layout.box()
        box.label(text="Global Settings:", icon='PREFERENCES')
        settings = context.scene.ultimate_settings
        box.prop(settings, "show_tooltips")
        box.prop(settings, "compact_ui")
        box.prop(settings, "auto_refresh")

# Property group for addon-wide settings
class ULTIMATE_PG_settings(bpy.types.PropertyGroup):
    """Global settings for Ultimate Blender Addon"""
    
    # UI preferences
    show_tooltips: bpy.props.BoolProperty(
        name="Show Tooltips",
        description="Show detailed tooltips for all tools",
        default=True
    )
    
    compact_ui: bpy.props.BoolProperty(
        name="Compact UI",
        description="Use compact interface layout",
        default=False
    )
    
    # Tool preferences
    auto_refresh: bpy.props.BoolProperty(
        name="Auto Refresh",
        description="Automatically refresh tool data when scene changes",
        default=True
    )

classes = (
    ULTIMATE_PG_settings,
    ULTIMATE_PT_main_panel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # Register global settings
    bpy.types.Scene.ultimate_settings = bpy.props.PointerProperty(type=ULTIMATE_PG_settings)

def unregister():
    # Remove global settings
    if hasattr(bpy.types.Scene, 'ultimate_settings'):
        del bpy.types.Scene.ultimate_settings
    
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
