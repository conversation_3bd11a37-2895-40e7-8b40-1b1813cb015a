# Render Range and Notes - Adapted for Ultimate Blender Addon
# Original by <PERSON><PERSON><PERSON> Bob

import bpy

class ULTIMATE_PG_RenderRangeSettings(bpy.types.PropertyGroup):
    """Settings for render range and notes functionality"""
    start: bpy.props.StringProperty(
        name="Start",
        description="Custom Start field",
        default=""
    )
    end: bpy.props.StringProperty(
        name="End",
        description="Custom End field",
        default=""
    )
    notes: bpy.props.StringProperty(
        name="Notes",
        description="Custom Notes field for writing longer text",
        default=""
    )
    note_2: bpy.props.StringProperty(
        name="Note 2",
        description="Custom Note 2 field",
        default=""
    )
    note_3: bpy.props.StringProperty(
        name="Note 3",
        description="Custom Note 3 field",
        default=""
    )
    note_4: bpy.props.StringProperty(
        name="Note 4",
        description="Custom Note 4 field",
        default=""
    )
    note_5: bpy.props.StringProperty(
        name="Note 5",
        description="Custom Note 5 field",
        default=""
    )

class ULTIMATE_ANIM_OT_copy_start_to_frame_start(bpy.types.Operator):
    """Copy the value from Start to the frame start"""
    bl_idname = "ultimate_anim.copy_start_to_frame_start"
    bl_label = "Copy to Start"
    bl_description = "Copy the value from Start to the frame start"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        settings = context.scene.ultimate_render_range_settings
        if settings.start:
            try:
                context.scene.frame_start = int(settings.start)
                self.report({'INFO'}, f"Frame start set to {settings.start}")
            except ValueError:
                self.report({'WARNING'}, "Invalid value for Start")
        return {'FINISHED'}

class ULTIMATE_ANIM_OT_copy_end_to_frame_end(bpy.types.Operator):
    """Copy the value from End to the frame end"""
    bl_idname = "ultimate_anim.copy_end_to_frame_end"
    bl_label = "Copy to End"
    bl_description = "Copy the value from End to the frame end"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        settings = context.scene.ultimate_render_range_settings
        if settings.end:
            try:
                context.scene.frame_end = int(settings.end)
                self.report({'INFO'}, f"Frame end set to {settings.end}")
            except ValueError:
                self.report({'WARNING'}, "Invalid value for End")
        return {'FINISHED'}

class ULTIMATE_ANIM_OT_copy_current_range(bpy.types.Operator):
    """Copy current frame start/end to the custom fields"""
    bl_idname = "ultimate_anim.copy_current_range"
    bl_label = "Copy Current Range"
    bl_description = "Copy current frame start and end to the custom fields"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        settings = context.scene.ultimate_render_range_settings
        settings.start = str(context.scene.frame_start)
        settings.end = str(context.scene.frame_end)
        self.report({'INFO'}, f"Copied range {settings.start}-{settings.end}")
        return {'FINISHED'}

class ULTIMATE_ANIM_OT_apply_range(bpy.types.Operator):
    """Apply both start and end values to frame range"""
    bl_idname = "ultimate_anim.apply_range"
    bl_label = "Apply Range"
    bl_description = "Apply both start and end values to frame range"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        settings = context.scene.ultimate_render_range_settings
        
        start_success = False
        end_success = False
        
        if settings.start:
            try:
                context.scene.frame_start = int(settings.start)
                start_success = True
            except ValueError:
                self.report({'WARNING'}, "Invalid value for Start")
        
        if settings.end:
            try:
                context.scene.frame_end = int(settings.end)
                end_success = True
            except ValueError:
                self.report({'WARNING'}, "Invalid value for End")
        
        if start_success and end_success:
            self.report({'INFO'}, f"Applied range {settings.start}-{settings.end}")
        elif start_success:
            self.report({'INFO'}, f"Applied start frame {settings.start}")
        elif end_success:
            self.report({'INFO'}, f"Applied end frame {settings.end}")
        else:
            self.report({'WARNING'}, "No valid values to apply")
            
        return {'FINISHED'}

class ULTIMATE_ANIM_OT_clear_notes(bpy.types.Operator):
    """Clear all notes"""
    bl_idname = "ultimate_anim.clear_notes"
    bl_label = "Clear Notes"
    bl_description = "Clear all note fields"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        settings = context.scene.ultimate_render_range_settings
        settings.notes = ""
        settings.note_2 = ""
        settings.note_3 = ""
        settings.note_4 = ""
        settings.note_5 = ""
        self.report({'INFO'}, "All notes cleared")
        return {'FINISHED'}

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_PG_RenderRangeSettings,
    ULTIMATE_ANIM_OT_copy_start_to_frame_start,
    ULTIMATE_ANIM_OT_copy_end_to_frame_end,
    ULTIMATE_ANIM_OT_copy_current_range,
    ULTIMATE_ANIM_OT_apply_range,
    ULTIMATE_ANIM_OT_clear_notes,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    bpy.types.Scene.ultimate_render_range_settings = bpy.props.PointerProperty(type=ULTIMATE_PG_RenderRangeSettings)

def unregister():
    if hasattr(bpy.types.Scene, 'ultimate_render_range_settings'):
        del bpy.types.Scene.ultimate_render_range_settings
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
