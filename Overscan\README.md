# overscan
Overscan is a Blender addon that allows you to render your image with an overscan, with three different options.

Once installed, a new Overscan option will apprea in the Output settings under the reder resolution. 

MODES:

Percentage will allow you to specify a percentage that you would like to add to the render resolution. For example, if the original version is 1920x1080 and you add 10%, you will endup with a resoultion of 2112x1188.

Extra pixel will add the desired amount of pixel in the overscan area of the image. So if you ask 100, you will get 2020x1180.

Specific X resolution will set the X resoution to the input and adapt the y resolution to keep the same aspect ratio.

Note that for every change that you make you need to press the reset button first.

This will work for any rendering engine.

The name of the camera will change, adding _o to indicate that it has been set to overscan. If you reset the overscan, the name will revert.

You can see a video description at https://www.youtube.com/watch?v=yXSfgBu5Rac

Also watch this video for the latest functions:

https://youtu.be/yXSfgBu5Rac

