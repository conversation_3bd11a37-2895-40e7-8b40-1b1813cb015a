#!/usr/bin/env python3
"""
Package the Ultimate Blender Addon for installation
Creates a .zip file that can be installed in Blender
"""

import os
import zipfile
import shutil
from pathlib import Path

def create_addon_package():
    """Create installable .zip package for the Ultimate Blender Addon"""
    
    # Get the current directory (should be UltimateBlenderAddon)
    addon_dir = Path(__file__).parent
    
    # Define the package name
    package_name = "UltimateBlenderAddon.zip"
    package_path = addon_dir.parent / package_name
    
    # Remove existing package if it exists
    if package_path.exists():
        package_path.unlink()
        print(f"Removed existing package: {package_path}")
    
    # Files and directories to include in the package
    include_patterns = [
        "__init__.py",
        "blender_manifest.toml",
        "modules/",
        "ui/",
    ]
    
    # Files and directories to exclude
    exclude_patterns = [
        "__pycache__",
        "*.pyc",
        "*.pyo",
        ".git",
        ".gitignore",
        "package_addon.py",
        "test_structure.py",
    ]
    
    def should_include(file_path):
        """Check if a file should be included in the package"""
        file_str = str(file_path)
        
        # Check exclude patterns
        for pattern in exclude_patterns:
            if pattern in file_str:
                return False
        
        return True
    
    # Create the zip package
    with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Add all files recursively
        for root, dirs, files in os.walk(addon_dir):
            # Filter out excluded directories
            dirs[:] = [d for d in dirs if not any(pattern in d for pattern in exclude_patterns)]
            
            for file in files:
                file_path = Path(root) / file
                
                if should_include(file_path):
                    # Calculate the archive path (relative to addon_dir)
                    archive_path = file_path.relative_to(addon_dir.parent)
                    zipf.write(file_path, archive_path)
                    print(f"Added: {archive_path}")
    
    print(f"\n✓ Package created successfully: {package_path}")
    print(f"✓ Package size: {package_path.stat().st_size / 1024:.1f} KB")
    
    return package_path

def verify_package(package_path):
    """Verify the package contents"""
    print(f"\n--- Verifying package: {package_path.name} ---")
    
    with zipfile.ZipFile(package_path, 'r') as zipf:
        files = zipf.namelist()
        
        # Check for required files
        required_files = [
            "UltimateBlenderAddon/__init__.py",
            "UltimateBlenderAddon/blender_manifest.toml",
        ]
        
        for req_file in required_files:
            if req_file in files:
                print(f"✓ {req_file}")
            else:
                print(f"✗ Missing: {req_file}")
        
        # Count files by type
        py_files = [f for f in files if f.endswith('.py')]
        toml_files = [f for f in files if f.endswith('.toml')]
        
        print(f"\n📊 Package contents:")
        print(f"  • Python files: {len(py_files)}")
        print(f"  • TOML files: {len(toml_files)}")
        print(f"  • Total files: {len(files)}")
        
        # Show directory structure
        print(f"\n📁 Directory structure:")
        dirs = set()
        for file in files:
            parts = Path(file).parts
            for i in range(1, len(parts)):
                dirs.add('/'.join(parts[:i]))
        
        for dir_path in sorted(dirs):
            level = dir_path.count('/')
            indent = "  " * level
            dir_name = dir_path.split('/')[-1]
            print(f"{indent}📁 {dir_name}/")

if __name__ == "__main__":
    print("🚀 Creating Ultimate Blender Addon package...")
    
    try:
        package_path = create_addon_package()
        verify_package(package_path)
        
        print(f"\n🎉 Success! Your addon is ready for installation.")
        print(f"📦 Install in Blender: Edit > Preferences > Add-ons > Install > {package_path.name}")
        
    except Exception as e:
        print(f"\n❌ Error creating package: {e}")
        import traceback
        traceback.print_exc()
