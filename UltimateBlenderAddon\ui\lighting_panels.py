# Lighting Panels for Ultimate Blender Addon
# Unified interface for all lighting tools

import bpy
from bpy.types import Panel

class ULTIMATE_PT_lighting_panel(Panel):
    """Main lighting tools panel"""
    bl_label = "Lighting Tools"
    bl_idname = "ULTIMATE_PT_lighting_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_order = 4

    def draw(self, context):
        layout = self.layout
        layout.label(text="Lighting tools will be added here")
        layout.label(text="(Light Editor, Light Linking, etc.)")

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_PT_lighting_panel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
