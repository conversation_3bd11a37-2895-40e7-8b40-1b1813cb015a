bl_info = {
    "name": "Ultimate Blender Addon",
    "author": "<PERSON><PERSON><PERSON> Bob (<PERSON>) - Combined Collection",
    "version": (1, 0, 0),
    "blender": (4, 2, 0),
    "location": "View3D > Sidebar > Ultimate Tab",
    "description": "A comprehensive collection of Blender tools including animation, modeling, rendering, lighting, and reference utilities",
    "category": "3D View",
    "support": "COMMUNITY",
    "doc_url": "https://github.com/riouxr/UltimateBlenderAddon",
    "tracker_url": "https://github.com/riouxr/UltimateBlenderAddon/issues",
    "warning": "",
}

import bpy
import sys
import os

# Add the addon directory to Python path for imports
addon_dir = os.path.dirname(__file__)
if addon_dir not in sys.path:
    sys.path.append(addon_dir)

# Import all modules
from . import modules
from . import ui

# List of all modules to register
module_list = [
    modules.animation,
    modules.modeling, 
    modules.rendering,
    modules.lighting,
    modules.reference,
    modules.viewport,
    modules.utils,
    ui,
]

def register():
    """Register all addon modules and classes"""
    print("Registering Ultimate Blender Addon...")
    
    # Register modules in order
    for module in module_list:
        try:
            if hasattr(module, 'register'):
                module.register()
                print(f"  ✓ Registered {module.__name__}")
        except Exception as e:
            print(f"  ✗ Failed to register {module.__name__}: {e}")
            # Continue with other modules even if one fails
            continue
    
    print("Ultimate Blender Addon registration complete!")

def unregister():
    """Unregister all addon modules and classes"""
    print("Unregistering Ultimate Blender Addon...")
    
    # Unregister modules in reverse order
    for module in reversed(module_list):
        try:
            if hasattr(module, 'unregister'):
                module.unregister()
                print(f"  ✓ Unregistered {module.__name__}")
        except Exception as e:
            print(f"  ✗ Failed to unregister {module.__name__}: {e}")
            # Continue with other modules even if one fails
            continue
    
    # Clean up Python path
    if addon_dir in sys.path:
        sys.path.remove(addon_dir)
    
    print("Ultimate Blender Addon unregistration complete!")

if __name__ == "__main__":
    register()
