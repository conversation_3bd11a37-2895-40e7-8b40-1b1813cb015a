# Viewport Panels for Ultimate Blender Addon
# Unified interface for all viewport tools

import bpy
from bpy.types import Panel

class ULTIMATE_PT_viewport_panel(Panel):
    """Main viewport tools panel"""
    bl_label = "Viewport Tools"
    bl_idname = "ULTIMATE_PT_viewport_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_order = 6

    def draw(self, context):
        layout = self.layout

        # Camera and Cursor Lock Tools
        box = layout.box()
        box.label(text="Camera & Cursor Lock", icon='CAMERA_DATA')

        row = box.row(align=True)
        row.operator("ultimate_viewport.toggle_lock_camera", text="Toggle Camera Lock", icon='CAMERA_DATA')
        row.operator("ultimate_viewport.toggle_lock_cursor", text="Toggle Cursor Lock", icon='CURSOR')

        # Fire Ray Tool
        box = layout.box()
        box.label(text="Tracking Tools", icon='TRACKING')

        col = box.column(align=True)
        col.operator("ultimate_viewport.fire_ray", text="Fire Ray", icon='TRACKING_FORWARDS')
        col.label(text="Select empty, then fire ray from camera", icon='INFO')

        # Placeholder for future tools
        layout.separator()
        layout.label(text="Additional viewport tools:", icon='TOOL_SETTINGS')
        layout.label(text="• Object Display (coming soon)")
        layout.label(text="• View utilities (coming soon)")

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_PT_viewport_panel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
