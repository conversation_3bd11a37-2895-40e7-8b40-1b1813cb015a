# Viewport Panels for Ultimate Blender Addon
# Unified interface for all viewport tools

import bpy
from bpy.types import Panel

class ULTIMATE_PT_viewport_panel(Panel):
    """Main viewport tools panel"""
    bl_label = "Viewport Tools"
    bl_idname = "ULTIMATE_PT_viewport_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Ultimate'
    bl_order = 6

    def draw(self, context):
        layout = self.layout
        layout.label(text="Viewport tools will be added here")
        layout.label(text="(Object Display, Camera Lock, etc.)")

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_PT_viewport_panel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
