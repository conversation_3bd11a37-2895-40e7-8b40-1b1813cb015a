# Modules package for Ultimate Blender Addon
# This package contains all the functional modules organized by category

from . import animation
from . import modeling
from . import rendering
from . import lighting
from . import reference
from . import viewport
from . import utils

def register():
    """Register all module categories"""
    animation.register()
    modeling.register()
    rendering.register()
    lighting.register()
    reference.register()
    viewport.register()
    utils.register()

def unregister():
    """Unregister all module categories in reverse order"""
    utils.unregister()
    viewport.unregister()
    reference.unregister()
    lighting.unregister()
    rendering.unregister()
    modeling.unregister()
    animation.unregister()
