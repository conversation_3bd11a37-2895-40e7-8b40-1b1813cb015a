# Fire Ray - Adapted for Ultimate Blender Addon
# Create tracking rays from camera to empty objects
# Original by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>buk

import bpy
from mathutils import Vector
from ...utils.common import report_info, report_error, get_selected_objects_safe

class ULTIMATE_VIEWPORT_OT_fire_ray(bpy.types.Operator):
    """Draw mesh between camera and empty for tracking"""
    bl_idname = "ultimate_viewport.fire_ray"
    bl_label = "Fire Ray"
    bl_description = "Create an edge from camera through selected empty for tracking"
    bl_options = {'REGISTER', 'UNDO'}

    @classmethod
    def poll(cls, context):
        """Check if we have an empty selected"""
        sel_objs = get_selected_objects_safe()
        for obj in sel_objs:
            if obj.type == 'EMPTY':
                return True
        return False

    def execute(self, context):
        # Get main camera
        maincam = context.scene.camera
        if not maincam:
            report_error(self, "No active camera in scene")
            return {'CANCELLED'}

        # Find first selected empty
        sel_objs = get_selected_objects_safe()
        first_sel_empty = None

        for obj in sel_objs:
            if obj.type == 'EMPTY':
                first_sel_empty = obj
                break

        if not first_sel_empty:
            report_error(self, "No empty selected")
            return {'CANCELLED'}

        # Clean up existing fire ray curves
        objs_to_delete = []
        for obj in context.scene.objects:
            if obj.name.startswith('UltimateFireRay'):
                objs_to_delete.append(obj)

        for object_to_delete in objs_to_delete:
            bpy.data.objects.remove(object_to_delete, do_unlink=True)

        # Create new curve
        new_curve = bpy.data.curves.new('ultimate_fire_ray_curve', type='CURVE')
        new_curve.dimensions = '3D'
        path = new_curve.splines.new('POLY')

        # Set curve points
        path.points.add(2)
        path.points[0].co = Vector((0, 0, 0, 1))

        # Calculate direction from camera to empty
        main_cam_translation = maincam.matrix_world.to_translation()
        vec_e = Vector((first_sel_empty.location.x, first_sel_empty.location.y, first_sel_empty.location.z, 1))
        vec_c = Vector((main_cam_translation.x, main_cam_translation.y, main_cam_translation.z, 1))
        path.points[1].co = (vec_e - vec_c) * 1000

        # Create curve object
        curve_obj = bpy.data.objects.new('UltimateFireRay', new_curve)
        curve_obj.location = main_cam_translation
        context.scene.collection.objects.link(curve_obj)

        # Convert to mesh
        context.view_layer.objects.active = curve_obj
        curve_obj.select_set(True)
        bpy.ops.object.convert(target='MESH')

        # Reselect the empty
        context.view_layer.objects.active = first_sel_empty
        curve_obj.select_set(False)
        first_sel_empty.select_set(True)

        report_info(self, f"Fire ray created from camera to {first_sel_empty.name}")
        return {'FINISHED'}

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_VIEWPORT_OT_fire_ray,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    print("    Fire Ray registration complete")

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
