# Link Manager - Adapted for Ultimate Blender Addon
# Manage linked files with expand toggle, relocate, reload, delete and add link buttons
# Original by Blen<PERSON> Bob

import bpy
import os
from bpy_extras.io_utils import ImportHelper
from bpy.props import StringProperty
from ...utils.common import report_info, report_error

# Global state management
library_order = []
expanded_states = {}
link_active_states = {}

def normalize_filepath(filepath):
    """Return Blender-style forward-slash path (relative if prefs allow)."""
    abs_path = bpy.path.abspath(filepath)
    if bpy.context.preferences.filepaths.use_relative_paths:
        try:
            rel = bpy.path.relpath(abs_path)
            return rel.replace("\\", "/")
        except ValueError:
            pass
    return abs_path.replace("\\", "/")

def force_viewport_refresh():
    """Redraw every 3D viewport and update view layer."""
    bpy.context.view_layer.update()
    for window in bpy.context.window_manager.windows:
        for area in window.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()

class ULTIMATE_REFERENCE_OT_toggle_expand(bpy.types.Operator):
    """Toggle expand/collapse state for linked file"""
    bl_idname = "ultimate_reference.toggle_expand"
    bl_label = "Toggle Expand"
    bl_description = "Toggle expand/collapse state for linked file"

    filepath: StringProperty()

    def execute(self, context):
        fp = normalize_filepath(self.filepath)
        expanded_states[fp] = not expanded_states.get(fp, False)
        return {'FINISHED'}

class ULTIMATE_REFERENCE_OT_load_unload(bpy.types.Operator):
    """Toggle visibility of linked file"""
    bl_idname = "ultimate_reference.load_unload"
    bl_label = "Toggle Visibility"
    bl_description = "Toggle visibility of linked file"

    filepath: StringProperty()

    def execute(self, context):
        fp = normalize_filepath(self.filepath)
        lib = next((l for l in bpy.data.libraries if normalize_filepath(l.filepath) == fp), None)

        if not lib:
            report_error(self, "Library not found")
            return {'CANCELLED'}

        # Toggle visibility state
        current_state = link_active_states.get(fp, True)
        link_active_states[fp] = not current_state

        # Hide/show objects from this library
        for obj in bpy.data.objects:
            if obj.library == lib:
                obj.hide_viewport = current_state
                obj.hide_render = current_state

        status = "hidden" if current_state else "visible"
        report_info(self, f"Library {os.path.basename(fp)} is now {status}")
        force_viewport_refresh()
        return {'FINISHED'}

class ULTIMATE_REFERENCE_OT_reload(bpy.types.Operator):
    """Reload linked file"""
    bl_idname = "ultimate_reference.reload"
    bl_label = "Reload"
    bl_description = "Reload linked file from disk"

    filepath: StringProperty()

    def execute(self, context):
        fp = normalize_filepath(self.filepath)
        lib = next((l for l in bpy.data.libraries if normalize_filepath(l.filepath) == fp), None)

        if not lib:
            report_error(self, "Library not found")
            return {'CANCELLED'}

        # Reload the library
        lib.reload()
        report_info(self, f"Reloaded {os.path.basename(fp)}")
        force_viewport_refresh()
        return {'FINISHED'}

class ULTIMATE_REFERENCE_OT_remove(bpy.types.Operator):
    """Remove linked file"""
    bl_idname = "ultimate_reference.remove"
    bl_label = "Remove"
    bl_description = "Remove linked file from scene"

    filepath: StringProperty()

    def execute(self, context):
        fp = normalize_filepath(self.filepath)
        lib = next((l for l in bpy.data.libraries if normalize_filepath(l.filepath) == fp), None)

        if not lib:
            report_error(self, "Library not found")
            return {'CANCELLED'}

        # Remove the library
        bpy.data.libraries.remove(lib)

        # Clean up state
        expanded_states.pop(fp, None)
        link_active_states.pop(fp, None)
        if fp in library_order:
            library_order.remove(fp)

        report_info(self, f"Removed {os.path.basename(fp)}")
        return {'FINISHED'}

class ULTIMATE_REFERENCE_OT_relocate(bpy.types.Operator, ImportHelper):
    """Relocate linked file to new path"""
    bl_idname = "ultimate_reference.relocate"
    bl_label = "Relocate"
    bl_description = "Relocate linked file to new path"

    original_filepath: StringProperty()
    filename_ext = ".blend"
    filter_glob: StringProperty(default="*.blend", options={'HIDDEN'})

    def execute(self, context):
        old_fp = normalize_filepath(self.original_filepath)
        new_fp = normalize_filepath(self.filepath)

        if not os.path.exists(bpy.path.abspath(new_fp)):
            report_error(self, f"File not found: {new_fp}")
            return {'CANCELLED'}

        lib = next((l for l in bpy.data.libraries if normalize_filepath(l.filepath) == old_fp), None)
        if not lib:
            report_error(self, "Library not found")
            return {'CANCELLED'}

        # Update library path
        lib.filepath = new_fp
        lib.reload()

        # Update state tracking
        if old_fp in library_order:
            idx = library_order.index(old_fp)
            library_order[idx] = new_fp

        if old_fp in link_active_states:
            link_active_states[new_fp] = link_active_states.pop(old_fp)

        if old_fp in expanded_states:
            expanded_states[new_fp] = expanded_states.pop(old_fp)

        report_info(self, f"Relocated to {os.path.basename(new_fp)}")
        force_viewport_refresh()
        return {'FINISHED'}

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_REFERENCE_OT_toggle_expand,
    ULTIMATE_REFERENCE_OT_load_unload,
    ULTIMATE_REFERENCE_OT_reload,
    ULTIMATE_REFERENCE_OT_remove,
    ULTIMATE_REFERENCE_OT_relocate,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    print("    Link Manager registration complete")

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
