# Common utilities for Ultimate Blender Addon
# Shared functions and utilities used across multiple modules

import bpy

def get_selected_objects_safe():
    """Safely get selected objects with error handling"""
    try:
        return bpy.context.selected_objects
    except:
        return []

def get_active_object_safe():
    """Safely get active object with error handling"""
    try:
        return bpy.context.active_object
    except:
        return None

def ensure_object_mode():
    """Ensure we're in object mode"""
    if bpy.context.mode != 'OBJECT':
        bpy.ops.object.mode_set(mode='OBJECT')

def report_info(operator, message):
    """Standard info reporting"""
    operator.report({'INFO'}, f"Ultimate Addon: {message}")

def report_warning(operator, message):
    """Standard warning reporting"""
    operator.report({'WARNING'}, f"Ultimate Addon: {message}")

def report_error(operator, message):
    """Standard error reporting"""
    operator.report({'ERROR'}, f"Ultimate Addon: {message}")
