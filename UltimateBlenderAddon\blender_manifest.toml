schema_version = "1.0.0"
id = "ultimate_blender_addon"
version = "1.0.0"
name = "Ultimate Blender Addon"
tagline = "Comprehensive collection of Blender tools for animation, modeling, rendering, lighting, and reference work"
maintainer = "Blen<PERSON> <PERSON> (<PERSON>) <<EMAIL>>"
type = "add-on"
website = "https://github.com/riouxr/UltimateBlenderAddon"

tags = ["Animation", "Modeling", "Render", "Lighting", "Mesh", "Camera", "3D View"]
license = ["SPDX:GPL-3.0-or-later"]
copyright = ["2025 Robert Rioux"]
blender_version_min = "4.2.0"

[build]
paths_exclude_pattern = [
    "__pycache__/",
    "*.pyc",
    ".git/",
    ".gitignore",
    "*.md"
]
