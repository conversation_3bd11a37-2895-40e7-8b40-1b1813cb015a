# Motion Path Creator - Adapted for Ultimate Blender Addon
# Original by <PERSON><PERSON><PERSON> Bob

import bpy
import bmesh
from mathutils import Vector
from bpy.props import *
import random
from colorsys import hsv_to_rgb

def random_saturated_color():
    h = random.random()             # Random hue [0, 1]
    s = 1.0                         # Full saturation
    v = 1.0                         # Full brightness
    r, g, b = hsv_to_rgb(h, s, v)
    return (r, g, b, 1.0)

def get_frame_range(ctx):
    s = ctx.scene.ultimate_motion_path_settings
    if s.use_timeline:
        return ctx.scene.frame_start, ctx.scene.frame_end
    return s.start_frame, s.end_frame

def set_selectable(obj, selectable):
    obj.hide_select = not selectable

def resize_icospheres(ctx):
    s = ctx.scene.ultimate_motion_path_settings
    for path in s.paths:
        markers = find_markers(path.name)
        for marker in markers:
            marker.scale = (s.icosphere_radius,) * 3

def update_path_color(self, ctx):
    curve_obj = bpy.data.objects.get(self.name)
    if curve_obj and curve_obj.data.materials:
        mat = curve_obj.data.materials[0]
        if mat and mat.use_nodes:
            bsdf = mat.node_tree.nodes.get("Principled BSDF")
            if bsdf:
                bsdf.inputs[0].default_value = self.color

class ULTIMATE_PG_MotionPathEntry(bpy.types.PropertyGroup):
    name: StringProperty()
    color: FloatVectorProperty(
        subtype='COLOR',
        size=4,
        default=(1, 0, 1, 1),
        min=0.0,
        max=1.0,
        update=update_path_color
    )
    show_markers: BoolProperty(default=True)
    locked: BoolProperty(default=True)
    ghosted: BoolProperty(default=False)
    visible: BoolProperty(default=True)
    source_type: StringProperty()
    object_name: StringProperty()
    markers_on_keyframes_only: BoolProperty(default=False)
    previous_show_markers: BoolProperty(default=True)

class ULTIMATE_PG_MotionPathSettings(bpy.types.PropertyGroup):
    use_timeline: BoolProperty(name="Use Timeline", default=True)
    start_frame: IntProperty(name="Start", default=1, min=1)
    end_frame: IntProperty(name="End", default=250, min=1)

    icosphere_radius: FloatProperty(
        name="Radius",
        default=0.01,
        min=0.001,
        max=10.0,
        update=lambda self, ctx: resize_icospheres(ctx)
    )

    marker_step: IntProperty(
        name="Steps",
        default=1,
        min=1,
        max=200,
        description="Interval between markers"
    )

    paths: CollectionProperty(type=ULTIMATE_PG_MotionPathEntry)
    active_path_index: IntProperty()

def find_markers(path_name):
    return [obj for obj in bpy.data.objects if obj.parent and obj.parent.name == path_name]

def delete_path_objects(path_name):
    objs_to_delete = [
        o for o in bpy.data.objects
        if o.name == path_name or (o.parent and o.parent.name == path_name)
    ]
    
    deleted_mats = set()
    for o in objs_to_delete:
        mat_name = o.get("motion_path_addon_material")
        if mat_name and mat_name not in deleted_mats:
            mat = bpy.data.materials.get(mat_name)
            if mat:
                bpy.data.materials.remove(mat, do_unlink=True)
                deleted_mats.add(mat_name)
        bpy.data.objects.remove(o, do_unlink=True)

def create_base_icosphere():
    bpy.ops.mesh.primitive_ico_sphere_add(subdivisions=1, radius=1)
    ico = bpy.context.active_object
    bpy.context.collection.objects.unlink(ico)
    return ico.data

def create_motion_path(obj, start, end, world_matrix, *, v_index=None, bone_name=None, path_data=None, source_type="object"):
    scene = bpy.context.scene
    settings = scene.ultimate_motion_path_settings
    base_mesh = create_base_icosphere()

    suffix = {
        "object": "MP_O",
        "vertex": "MP_V", 
        "bone": "MP_B"
    }.get(source_type, "MP_X")

    if source_type == "vertex" and v_index is not None:
        full_name = f"{obj.name}_{v_index}.{suffix}"
    elif source_type == "bone" and bone_name:
        full_name = f"{obj.name}_{bone_name}.{suffix}"
    else:
        full_name = f"{obj.name}.{suffix}"

    curve_data = bpy.data.curves.new(name=full_name + ".Data", type='CURVE')
    curve_data.dimensions = '3D'
    curve_data.resolution_u = 2

    curve_obj = bpy.data.objects.new(full_name, curve_data)
    curve_obj["is_motion_path"] = True
    scene.collection.objects.link(curve_obj)
    curve_obj.show_in_front = True
    set_selectable(curve_obj, False)

    spline = curve_data.splines.new('POLY')
    spline.points.add(end - start)

    mat_color = path_data.color if path_data else random_saturated_color()
    mat_name = f"MP_Mat_{full_name}"
    
    # Create material
    mat = bpy.data.materials.new(name=mat_name)
    mat.use_nodes = True
    bsdf = mat.node_tree.nodes["Principled BSDF"]
    bsdf.inputs[0].default_value = mat_color
    curve_obj.data.materials.append(mat)
    curve_obj["motion_path_addon_material"] = mat_name

    # Store path data
    if not path_data:
        path_entry = settings.paths.add()
        path_entry.name = full_name
        path_entry.color = mat_color
        path_entry.object_name = obj.name
        path_entry.source_type = source_type

    # Calculate positions and create markers
    positions = []
    for frame in range(start, end + 1):
        scene.frame_set(frame)
        
        if source_type == "vertex":
            vert = obj.data.vertices[v_index]
            world_pos = obj.matrix_world @ vert.co
        elif source_type == "bone":
            bone = obj.pose.bones[bone_name]
            world_pos = obj.matrix_world @ bone.matrix @ Vector((0, 0, 0))
        else:
            world_pos = obj.matrix_world.translation
            
        positions.append(world_pos)

    # Set curve points
    for i, pos in enumerate(positions):
        spline.points[i].co = (*pos, 1.0)

    # Create markers
    for i, pos in enumerate(positions):
        if i % settings.marker_step == 0:
            marker = bpy.data.objects.new(f"Marker_{i}", base_mesh.copy())
            marker.location = pos
            marker.scale = (settings.icosphere_radius,) * 3
            marker.parent = curve_obj
            scene.collection.objects.link(marker)
            set_selectable(marker, False)

    return curve_obj

# ────────────────────────────────────────────────────────────────────────────────
# Operators
# ────────────────────────────────────────────────────────────────────────────────

class ULTIMATE_ANIM_OT_motion_path_create_object(bpy.types.Operator):
    """Create motion paths for selected objects"""
    bl_idname = "ultimate_anim.motion_path_create_object"
    bl_label = "Object Paths"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        if bpy.context.mode != 'OBJECT':
            self.report({'WARNING'}, "Must be in Object Mode to create object paths.")
            return {'CANCELLED'}

        objs = context.selected_objects
        if not objs:
            self.report({'WARNING'}, "No selected objects")
            return {'CANCELLED'}

        f0, f1 = get_frame_range(context)

        for obj in objs:
            name = f"{obj.name}.MP_O"
            if bpy.data.objects.get(name):
                self.report({'INFO'}, f"Path already exists for {obj.name}")
                continue
            create_motion_path(obj, f0, f1, obj.matrix_world, source_type='object')

        return {'FINISHED'}

class ULTIMATE_ANIM_OT_motion_path_create_vertex(bpy.types.Operator):
    """Create motion paths for selected vertices"""
    bl_idname = "ultimate_anim.motion_path_create_vertex"
    bl_label = "Vertex Paths"
    bl_options = {'REGISTER', 'UNDO'}

    confirm: BoolProperty(default=False)

    def execute(self, context):
        if context.mode != 'EDIT_MESH':
            self.report({'WARNING'}, "Must be in Edit Mode to create vertex paths.")
            return {'CANCELLED'}

        obj = context.active_object
        if not obj or obj.type != 'MESH':
            self.report({'WARNING'}, "Active object must be a mesh.")
            return {'CANCELLED'}

        bm = bmesh.from_edit_mesh(obj.data)
        selected_verts = [v for v in bm.verts if v.select]

        if not selected_verts:
            self.report({'WARNING'}, "No vertices selected.")
            return {'CANCELLED'}

        if len(selected_verts) > 10 and not self.confirm:
            self.report({'WARNING'}, f"Creating {len(selected_verts)} paths. Use 'Confirm' if intended.")
            return {'CANCELLED'}

        f0, f1 = get_frame_range(context)
        bpy.ops.object.mode_set(mode='OBJECT')

        for vert in selected_verts:
            v_index = vert.index
            name = f"{obj.name}_{v_index}.MP_V"
            if bpy.data.objects.get(name):
                continue
            create_motion_path(obj, f0, f1, obj.matrix_world, v_index=v_index, source_type='vertex')

        bpy.ops.object.mode_set(mode='EDIT')
        return {'FINISHED'}

class ULTIMATE_ANIM_OT_motion_path_create_bone(bpy.types.Operator):
    """Create motion paths for selected bones"""
    bl_idname = "ultimate_anim.motion_path_create_bone"
    bl_label = "Bone Paths"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        armatures = [obj for obj in context.selected_objects if obj.type == 'ARMATURE']
        if not armatures:
            self.report({'WARNING'}, "No armatures selected.")
            return {'CANCELLED'}

        original_mode = context.mode
        f0, f1 = get_frame_range(context)
        created = 0

        for arm in armatures:
            bpy.ops.object.mode_set(mode='OBJECT')
            context.view_layer.objects.active = arm
            bpy.ops.object.mode_set(mode='POSE')

            for bone in arm.data.bones:
                if not bone.select:
                    continue

                path_name = f"{arm.name}_{bone.name}.MP_B"
                if bpy.data.objects.get(path_name):
                    self.report({'INFO'}, f"Path already exists for bone '{bone.name}' in '{arm.name}'")
                    continue

                create_motion_path(arm, f0, f1, arm.matrix_world, bone_name=bone.name, source_type='bone')
                created += 1

        if original_mode != 'OBJECT':
            bpy.ops.object.mode_set(mode=original_mode)

        if created == 0:
            return {'CANCELLED'}

        return {'FINISHED'}

class ULTIMATE_ANIM_OT_motion_path_delete_all(bpy.types.Operator):
    """Delete all motion paths"""
    bl_idname = "ultimate_anim.motion_path_delete_all"
    bl_label = "Delete All Paths"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        settings = context.scene.ultimate_motion_path_settings

        for path in reversed(settings.paths):
            delete_path_objects(path.name)
            delete_path_objects(path.name + "_Ghost")

        settings.paths.clear()
        return {'FINISHED'}

class ULTIMATE_ANIM_OT_motion_path_refresh_all(bpy.types.Operator):
    """Refresh all motion paths"""
    bl_idname = "ultimate_anim.motion_path_refresh_all"
    bl_label = "Refresh All Paths"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        settings = context.scene.ultimate_motion_path_settings
        f0, f1 = get_frame_range(context)

        for path in settings.paths:
            obj = bpy.data.objects.get(path.object_name)
            if not obj:
                self.report({'WARNING'}, f"Object '{path.object_name}' not found.")
                continue

            # Delete and recreate path
            delete_path_objects(path.name)

            if path.source_type == 'object':
                create_motion_path(obj, f0, f1, obj.matrix_world, path_data=path, source_type='object')
            elif path.source_type == 'vertex':
                try:
                    basename = path.name.rsplit(".MP_", 1)[0]
                    v_index = int(basename.rsplit("_", 1)[1])
                    create_motion_path(obj, f0, f1, obj.matrix_world, v_index=v_index, path_data=path, source_type='vertex')
                except Exception as e:
                    self.report({'WARNING'}, f"Could not refresh vertex path '{path.name}': {e}")
            elif path.source_type == 'bone':
                try:
                    bone_name = path.name.split("_", 1)[1].rsplit(".MP_", 1)[0]
                    create_motion_path(obj, f0, f1, obj.matrix_world, bone_name=bone_name, path_data=path, source_type='bone')
                except Exception as e:
                    self.report({'WARNING'}, f"Could not refresh bone path '{path.name}': {e}")

        return {'FINISHED'}

# ────────────────────────────────────────────────────────────────────────────────
# Registration
# ────────────────────────────────────────────────────────────────────────────────

classes = (
    ULTIMATE_PG_MotionPathEntry,
    ULTIMATE_PG_MotionPathSettings,
    ULTIMATE_ANIM_OT_motion_path_create_object,
    ULTIMATE_ANIM_OT_motion_path_create_vertex,
    ULTIMATE_ANIM_OT_motion_path_create_bone,
    ULTIMATE_ANIM_OT_motion_path_delete_all,
    ULTIMATE_ANIM_OT_motion_path_refresh_all,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)
    bpy.types.Scene.ultimate_motion_path_settings = PointerProperty(type=ULTIMATE_PG_MotionPathSettings)

def unregister():
    if hasattr(bpy.types.Scene, 'ultimate_motion_path_settings'):
        del bpy.types.Scene.ultimate_motion_path_settings
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
